<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}عدستي - متجر العدسات اللاصقة{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/style.css' %}">
    
    {% block extra_css %}{% endblock %}


</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="{% url 'home' %}">
                <i class="fas fa-eye me-2"></i>عدستي
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'home' %}">الرئيسية</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            المنتجات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'products:list' %}">جميع المنتجات</a></li>
                            <li><a class="dropdown-item" href="{% url 'products:list' %}?lens_type=daily">عدسات يومية</a></li>
                            <li><a class="dropdown-item" href="{% url 'products:list' %}?lens_type=monthly">عدسات شهرية</a></li>
                            <li><a class="dropdown-item" href="{% url 'products:list' %}?lens_usage=cosmetic">عدسات تجميلية</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">اتصل بنا</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Search -->
                    <li class="nav-item">
                        <form class="d-flex me-3" method="GET" action="{% url 'products:list' %}">
                            <input class="form-control form-control-sm" type="search" name="q" placeholder="البحث عن المنتجات...">
                            <button class="btn btn-outline-light btn-sm" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </li>
                    
                    <!-- Cart -->
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="{% url 'orders:cart' %}">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ cart_items_count|default:0 }}
                            </span>
                        </a>
                    </li>
                    

                    <!-- User Menu -->
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ user.first_name|default:user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
                                <li><a class="dropdown-item" href="{% url 'orders:list' %}">طلباتي</a></li>
                                {% if user.is_staff %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-primary fw-bold" href="{% url 'dashboard:home' %}"
                                           style="background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(44, 62, 80, 0.1));
                                                  border-radius: 8px;
                                                  margin: 0.25rem;">
                                        <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                        <span class="badge bg-primary ms-2" style="font-size: 0.7rem;">مدير</span>
                                    </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:login' %}">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounts:register' %}">إنشاء حساب</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-eye me-2"></i>عدستي</h5>
                    <p>متجرك الموثوق للعدسات اللاصقة الطبية والتجميلية. نوفر أفضل الماركات العالمية بأسعار منافسة.</p>
                </div>
                <div class="col-md-2">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'home' %}" class="text-light text-decoration-none">الرئيسية</a></li>
                        <li><a href="{% url 'products:list' %}" class="text-light text-decoration-none">المنتجات</a></li>
                        <li><a href="#about" class="text-light text-decoration-none">من نحن</a></li>
                        <li><a href="#contact" class="text-light text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>خدمة العملاء</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-phone me-2"></i>+964 ************</li>
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li><i class="fas fa-clock me-2"></i>السبت - الخميس: 9ص - 10م</li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h6>تابعنا</h6>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-light"><i class="fab fa-facebook fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-twitter fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-instagram fa-2x"></i></a>
                        <a href="#" class="text-light"><i class="fab fa-whatsapp fa-2x"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; 2024 عدستي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
