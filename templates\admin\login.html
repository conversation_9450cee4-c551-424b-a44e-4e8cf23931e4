{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style>
    body {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-color) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        padding: 2rem;
    }
    
    #header, #footer, .breadcrumbs {
        display: none !important;
    }
    
    #content {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        padding: 0;
        margin: 0;
        width: 100%;
        max-width: 400px;
        overflow: hidden;
    }
    
    .login-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        text-align: center;
        padding: 2rem;
        margin-bottom: 0;
    }
    
    .login-header h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 700;
    }
    
    .login-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }
    
    .login-icon {
        width: 80px;
        height: 80px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem auto;
        font-size: 2rem;
    }
    
    .login-form {
        padding: 2rem;
    }
    
    .form-row {
        margin-bottom: 1.5rem;
        padding: 0;
        border: none;
    }
    
    .form-row label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .form-row input[type="text"],
    .form-row input[type="password"] {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }
    
    .form-row input[type="text"]:focus,
    .form-row input[type="password"]:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        outline: none;
    }
    
    .submit-row {
        text-align: center;
        margin-top: 2rem;
        padding: 0;
    }
    
    .submit-row input[type="submit"] {
        width: 100%;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: 10px;
        color: white;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .submit-row input[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
    }
    
    .login-links {
        text-align: center;
        padding: 1rem 2rem 2rem 2rem;
        border-top: 1px solid #e9ecef;
        margin-top: 1rem;
    }
    
    .login-links a {
        color: var(--secondary-color);
        text-decoration: none;
        font-size: 0.9rem;
        margin: 0 0.5rem;
    }
    
    .login-links a:hover {
        text-decoration: underline;
    }
    
    .errornote {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        text-align: center;
        font-weight: 500;
    }
    
    .errornote ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    .errorlist {
        background: #ffe6e6;
        color: var(--danger-color);
        padding: 0.5rem;
        border-radius: 6px;
        margin-top: 0.5rem;
        font-size: 0.9rem;
    }
    
    .errorlist ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    @media (max-width: 480px) {
        body {
            padding: 1rem;
        }
        
        #content {
            max-width: 100%;
        }
        
        .login-header {
            padding: 1.5rem;
        }
        
        .login-form {
            padding: 1.5rem;
        }
        
        .login-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-header">
    <div class="login-icon">
        <i class="fas fa-eye"></i>
    </div>
    <h1>عدستي</h1>
    <p>لوحة الإدارة المتقدمة</p>
</div>

<div class="login-form">
    {% if form.errors and not form.non_field_errors %}
        <div class="errornote">
            <ul>
                {% for field in form %}
                    {% for error in field.errors %}
                        <li>{{ error|escape }}</li>
                    {% endfor %}
                {% endfor %}
            </ul>
        </div>
    {% endif %}

    {% if form.non_field_errors %}
        <div class="errornote">
            {{ form.non_field_errors }}
        </div>
    {% endif %}

    <form action="{{ app_path }}" method="post" id="login-form">
        {% csrf_token %}
        
        <div class="form-row">
            <label for="{{ form.username.id_for_label }}">{{ form.username.label_tag }}</label>
            {{ form.username }}
            {% if form.username.errors %}
                <div class="errorlist">{{ form.username.errors }}</div>
            {% endif %}
        </div>
        
        <div class="form-row">
            <label for="{{ form.password.id_for_label }}">{{ form.password.label_tag }}</label>
            {{ form.password }}
            {% if form.password.errors %}
                <div class="errorlist">{{ form.password.errors }}</div>
            {% endif %}
        </div>
        
        {% url 'admin_password_reset' as password_reset_url %}
        {% if password_reset_url %}
            <div class="password-reset-link">
                <a href="{{ password_reset_url }}">نسيت كلمة المرور؟</a>
            </div>
        {% endif %}
        
        <div class="submit-row">
            <input type="submit" value="تسجيل الدخول">
        </div>
        
        {% if user.is_authenticated %}
            <input type="hidden" name="next" value="{{ next }}">
        {% endif %}
    </form>
</div>

<div class="login-links">
    <a href="{% url 'home' %}">
        <i class="fas fa-home me-1"></i>العودة للموقع الرئيسي
    </a>
    |
    <a href="{% url 'dashboard:home' %}">
        <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
    </a>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on username field
    const usernameField = document.getElementById('{{ form.username.id_for_label }}');
    if (usernameField) {
        usernameField.focus();
    }
    
    // Add loading state to submit button
    const form = document.getElementById('login-form');
    const submitBtn = form.querySelector('input[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.value = 'جاري تسجيل الدخول...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
