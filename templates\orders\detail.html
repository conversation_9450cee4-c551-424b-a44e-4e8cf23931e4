{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل الطلب #{{ order.order_number }} - عدستي{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'orders:list' %}">طلباتي</a></li>
            <li class="breadcrumb-item active">طلب #{{ order.order_number }}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Order Header -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        طلب #{{ order.order_number }}
                    </h4>
                    <span class="badge badge-status-{{ order.status }} fs-6">
                        {% for status_code, status_name in order.STATUS_CHOICES %}
                            {% if status_code == order.status %}{{ status_name }}{% endif %}
                        {% endfor %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-calendar me-2"></i>تاريخ الطلب</h6>
                            <p class="text-muted">{{ order.created_at|date:"d F Y - H:i" }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-credit-card me-2"></i>حالة الدفع</h6>
                            <span class="badge badge-payment-{{ order.payment_status }}">
                                {% for payment_code, payment_name in order.PAYMENT_STATUS_CHOICES %}
                                    {% if payment_code == order.payment_status %}{{ payment_name }}{% endif %}
                                {% endfor %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-box me-2"></i>عناصر الطلب</h5>
                </div>
                <div class="card-body">
                    {% for item in order.items.all %}
                    <div class="row align-items-center mb-3 {% if not forloop.last %}border-bottom pb-3{% endif %}">
                        <div class="col-md-2">
                            {% if item.product.image %}
                                <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" 
                                     class="img-fluid rounded" style="max-height: 80px;">
                            {% else %}
                                <img src="{% static 'images/no-image.svg' %}" alt="لا توجد صورة" 
                                     class="img-fluid rounded" style="max-height: 80px;">
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">{{ item.product.name }}</h6>
                            <p class="text-muted mb-1">
                                <small>
                                    {% if item.product.brand %}{{ item.product.brand.name }} - {% endif %}
                                    {{ item.product.category.name }}
                                </small>
                            </p>
                            <p class="text-muted mb-0">
                                <small>{{ item.product.lens_type }} - {{ item.product.lens_usage }}</small>
                            </p>
                        </div>
                        <div class="col-md-2 text-center">
                            <span class="fw-bold">{{ item.quantity }}</span>
                            <br>
                            <small class="text-muted">الكمية</small>
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="fw-bold">{{ item.total }} د.ع</div>
                            <small class="text-muted">{{ item.price }} د.ع × {{ item.quantity }}</small>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد عناصر في هذا الطلب</p>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>معلومات الشحن</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>اسم المستلم</h6>
                            <p class="text-muted">{{ order.shipping_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>رقم الهاتف</h6>
                            <p class="text-muted">{{ order.shipping_phone }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>المدينة</h6>
                            <p class="text-muted">{{ order.shipping_city }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>الرمز البريدي</h6>
                            <p class="text-muted">{{ order.shipping_postal_code|default:"غير محدد" }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6>العنوان</h6>
                            <p class="text-muted">{{ order.shipping_address }}</p>
                        </div>
                    </div>
                    {% if order.notes %}
                    <div class="row">
                        <div class="col-12">
                            <h6>ملاحظات</h6>
                            <p class="text-muted">{{ order.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span>{{ order.subtotal }} د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span>
                            {% if order.shipping_cost > 0 %}
                                {{ order.shipping_cost }} د.ع
                            {% else %}
                                <span class="text-success">مجاني</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span>{{ order.tax_amount }} د.ع</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>الإجمالي:</strong>
                        <strong class="text-primary fs-5">{{ order.total_amount }} د.ع</strong>
                    </div>
                </div>
            </div>

            <!-- Order Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">إجراءات</h5>
                </div>
                <div class="card-body">
                    {% if order.status in 'pending,confirmed' %}
                        <form method="post" action="{% url 'orders:cancel' order.pk %}" class="mb-3">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-danger w-100" 
                                    onclick="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                                <i class="fas fa-times me-2"></i>إلغاء الطلب
                            </button>
                        </form>
                    {% endif %}
                    
                    <a href="{% url 'orders:list' %}" class="btn btn-outline-primary w-100 mb-2">
                        <i class="fas fa-list me-2"></i>جميع طلباتي
                    </a>
                    
                    {% if order.status == 'delivered' %}
                        <a href="#" class="btn btn-outline-success w-100">
                            <i class="fas fa-star me-2"></i>تقييم المنتجات
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.badge-status-pending { background-color: #ffc107; color: #000; }
.badge-status-confirmed { background-color: #17a2b8; color: #fff; }
.badge-status-processing { background-color: #fd7e14; color: #fff; }
.badge-status-shipped { background-color: #6f42c1; color: #fff; }
.badge-status-delivered { background-color: #28a745; color: #fff; }
.badge-status-cancelled { background-color: #dc3545; color: #fff; }
.badge-status-returned { background-color: #6c757d; color: #fff; }

.badge-payment-pending { background-color: #ffc107; color: #000; }
.badge-payment-paid { background-color: #28a745; color: #fff; }
.badge-payment-failed { background-color: #dc3545; color: #fff; }
.badge-payment-refunded { background-color: #6c757d; color: #fff; }
</style>
{% endblock %}
