# Generated by Django 4.2.7 on 2025-06-06 14:46

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='التقييم')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقييم')),
                ('comment', models.TextField(verbose_name='التعليق')),
                ('is_verified_purchase', models.BooleanField(default=False, verbose_name='شراء موثق')),
                ('is_approved', models.BooleanField(default=True, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التقييم')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='products.product', verbose_name='المنتج')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تقييم',
                'verbose_name_plural': 'التقييمات',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'product')},
            },
        ),
        migrations.CreateModel(
            name='ReviewHelpful',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_helpful', models.BooleanField(verbose_name='مفيد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التصويت')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='helpful_votes', to='reviews.review', verbose_name='التقييم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تصويت مفيد',
                'verbose_name_plural': 'التصويتات المفيدة',
                'unique_together': {('user', 'review')},
            },
        ),
    ]
