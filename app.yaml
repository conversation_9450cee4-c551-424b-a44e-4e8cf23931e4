# Google App Engine configuration for VisionLens

runtime: python311

# متغيرات البيئة
env_variables:
  DJANGO_SETTINGS_MODULE: visionlens_store.settings_production
  SECRET_KEY: "your-secret-key-here"
  DEBUG: "False"

# إعدادات الأداء
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

# إعدادات الموارد
resources:
  cpu: 1
  memory_gb: 0.5
  disk_size_gb: 10

# معالجات URL
handlers:
  # الملفات الثابتة
  - url: /static
    static_dir: staticfiles
    secure: always

  # ملفات الوسائط
  - url: /media
    static_dir: media
    secure: always

  # جميع الطلبات الأخرى
  - url: /.*
    script: auto
    secure: always

# إعدادات إضافية
entrypoint: gunicorn -b :$PORT visionlens_store.wsgi:application

# تخطي الملفات
skip_files:
  - ^(.*/)?#.*#$
  - ^(.*/)?.*~$
  - ^(.*/)?.*\.py[co]$
  - ^(.*/)?.*/RCS/.*$
  - ^(.*/)?\..*$
  - ^(.*/)?tests$
  - ^(.*/)?test_.*\.py$
  - ^Dockerfile$
  - ^README\..*$
  - \.gitignore
  - node_modules/
  - ^(.*/)?\.git/.*$
