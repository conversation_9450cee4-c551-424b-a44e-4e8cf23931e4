{% extends 'base/base.html' %}
{% load static %}

{% block title %}إتمام الطلب - عدستي{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'orders:cart' %}">سلة التسوق</a></li>
            <li class="breadcrumb-item active">إتمام الطلب</li>
        </ol>
    </nav>

    <h2 class="mb-4">إتمام الطلب</h2>

    <div class="row">
        <div class="col-lg-8">
            <!-- Shipping Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-shipping-fast me-2"></i>معلومات الشحن</h5>
                </div>
                <div class="card-body">
                    <form id="checkout-form">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" name="shipping_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" name="shipping_phone" required>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">العنوان الكامل *</label>
                            <textarea class="form-control" name="shipping_address" rows="3" required placeholder="الشارع، الحي، المنطقة..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المدينة *</label>
                                    <select class="form-select" name="shipping_city" required>
                                        <option value="">اختر المدينة</option>
                                        <option value="بغداد">بغداد</option>
                                        <option value="البصرة">البصرة</option>
                                        <option value="أربيل">أربيل</option>
                                        <option value="النجف">النجف</option>
                                        <option value="كربلاء">كربلاء</option>
                                        <option value="الموصل">الموصل</option>
                                        <option value="السليمانية">السليمانية</option>
                                        <option value="كركوك">كركوك</option>
                                        <option value="الأنبار">الأنبار</option>
                                        <option value="ديالى">ديالى</option>
                                        <option value="بابل">بابل</option>
                                        <option value="نينوى">نينوى</option>
                                        <option value="واسط">واسط</option>
                                        <option value="صلاح الدين">صلاح الدين</option>
                                        <option value="القادسية">القادسية</option>
                                        <option value="المثنى">المثنى</option>
                                        <option value="ذي قار">ذي قار</option>
                                        <option value="ميسان">ميسان</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الرمز البريدي</label>
                                    <input type="text" class="form-control" name="shipping_postal_code">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات إضافية</label>
                            <textarea class="form-control" name="notes" rows="2" placeholder="أي ملاحظات خاصة بالطلب..."></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Payment Method -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>طريقة الدفع</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="cash_on_delivery" value="cash" checked>
                                <label class="form-check-label" for="cash_on_delivery">
                                    <i class="fas fa-money-bill-wave text-success me-2"></i>
                                    الدفع عند الاستلام
                                </label>
                                <div class="form-text">ادفع نقداً عند وصول الطلب</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="payment_method" id="credit_card" value="card">
                                <label class="form-check-label" for="credit_card">
                                    <i class="fas fa-credit-card text-primary me-2"></i>
                                    بطاقة ائتمان
                                </label>
                                <div class="form-text">Visa, MasterCard</div>
                            </div>
                        </div>
                    </div>

                    <!-- Credit Card Form (Hidden by default) -->
                    <div id="card-details" class="d-none">
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">رقم البطاقة</label>
                                    <input type="text" class="form-control" placeholder="1234 5678 9012 3456">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">تاريخ الانتهاء</label>
                                    <input type="text" class="form-control" placeholder="MM/YY">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">CVV</label>
                                    <input type="text" class="form-control" placeholder="123">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">اسم حامل البطاقة</label>
                            <input type="text" class="form-control" placeholder="الاسم كما هو مكتوب على البطاقة">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    <!-- عرض عناصر السلة -->
                    {% if cart_items %}
                        {% for item in cart_items %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <small class="text-muted">{{ item.product.name }}</small>
                                <br>
                                <small>{{ item.quantity }} × {{ item.product.get_price }} د.ع</small>
                            </div>
                            <span>{{ item.total_price }} د.ع</span>
                        </div>
                        {% endfor %}
                        <hr>
                    {% endif %}

                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span>المجموع الفرعي:</span>
                            <span>{{ cart.total_price|default:"0" }} د.ع</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الشحن:</span>
                            <span class="text-success">مجاني</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>الضريبة:</span>
                            <span>0 د.ع</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>الإجمالي:</strong>
                            <strong class="text-primary">{{ cart.total_price|default:"0" }} د.ع</strong>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary w-100 btn-lg" onclick="submitOrder()">
                        <i class="fas fa-check me-2"></i>تأكيد الطلب
                    </button>

                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            معلوماتك محمية بأحدث تقنيات الأمان
                        </small>
                    </div>
                </div>
            </div>

            <!-- Security Features -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-4">
                            <i class="fas fa-truck fa-2x text-primary mb-2"></i>
                            <small class="d-block">شحن سريع</small>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                            <small class="d-block">دفع آمن</small>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-undo-alt fa-2x text-info mb-2"></i>
                            <small class="d-block">إرجاع مجاني</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Toggle credit card details
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const cardDetails = document.getElementById('card-details');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            if (this.value === 'card') {
                cardDetails.classList.remove('d-none');
            } else {
                cardDetails.classList.add('d-none');
            }
        });
    });
});

function submitOrder() {
    const form = document.getElementById('checkout-form');

    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Show loading
    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
    submitBtn.disabled = true;

    // إرسال الطلب إلى الخادم
    form.method = 'POST';
    form.action = '{% url "orders:checkout" %}';
    form.submit();
}
</script>
{% endblock %}
