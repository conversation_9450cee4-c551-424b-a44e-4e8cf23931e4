#!/bin/bash

# سكريبت نشر عدستي على Google Cloud Run

echo "🚀 بدء نشر عدستي على Google Cloud Run..."

# متغيرات المشروع
PROJECT_ID="visionlens-project"
SERVICE_NAME="visionlens-app"
REGION="us-central1"

# التحقق من تسجيل الدخول في Google Cloud
echo "🔐 التحقق من تسجيل الدخول..."
gcloud auth list

# تعيين المشروع
echo "📋 تعيين المشروع..."
gcloud config set project $PROJECT_ID

# تفعيل الخدمات المطلوبة
echo "⚙️ تفعيل الخدمات المطلوبة..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable sqladmin.googleapis.com

# بناء الصورة
echo "🏗️ بناء صورة Docker..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME

# نشر على Cloud Run
echo "🌐 نشر على Cloud Run..."
gcloud run deploy $SERVICE_NAME \
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --set-env-vars DJANGO_SETTINGS_MODULE=visionlens_store.settings_production \
    --memory 512Mi \
    --cpu 1 \
    --max-instances 10

echo "✅ تم النشر بنجاح!"
echo "🌐 رابط التطبيق: https://$SERVICE_NAME-$REGION.run.app"
