{% extends 'base/base.html' %}
{% load static %}

{% block title %}إنشاء حساب - عدستي{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <h2 class="h4 mb-2">إنشاء حساب جديد</h2>
                        <p class="text-muted">انضم إلى عائلة عدستي</p>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="{{ form.username.id_for_label }}" name="{{ form.username.name }}" value="{{ form.username.value|default:'' }}" required>
                            <div class="form-text">يجب أن يكون 150 حرف أو أقل. أحرف وأرقام و @/./+/-/_ فقط.</div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="{{ form.password1.id_for_label }}" name="{{ form.password1.name }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="{{ form.password2.id_for_label }}" name="{{ form.password2.name }}" required>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="#" class="text-decoration-none">الشروط والأحكام</a> و <a href="#" class="text-decoration-none">سياسة الخصوصية</a>
                            </label>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="newsletter">
                            <label class="form-check-label" for="newsletter">
                                أرغب في تلقي العروض والأخبار عبر البريد الإلكتروني
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                        </button>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">لديك حساب بالفعل؟</p>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary w-100 mt-2">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
