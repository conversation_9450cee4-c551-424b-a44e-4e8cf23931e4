# Generated by Django 4.2.7 on 2025-06-06 14:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العلامة التجارية')),
                ('logo', models.ImageField(blank=True, upload_to='brands/', verbose_name='شعار العلامة التجارية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'علامة تجارية',
                'verbose_name_plural': 'العلامات التجارية',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفئة')),
                ('name_en', models.CharField(max_length=100, verbose_name='اسم الفئة بالإنجليزية')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, upload_to='categories/', verbose_name='صورة الفئة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('name_en', models.CharField(max_length=200, verbose_name='اسم المنتج بالإنجليزية')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('description_en', models.TextField(verbose_name='الوصف بالإنجليزية')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر')),
                ('discount_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='سعر الخصم')),
                ('lens_type', models.CharField(choices=[('daily', 'يومية'), ('weekly', 'أسبوعية'), ('monthly', 'شهرية'), ('yearly', 'سنوية')], max_length=20, verbose_name='نوع العدسة')),
                ('lens_usage', models.CharField(choices=[('medical', 'طبية'), ('cosmetic', 'تجميلية'), ('both', 'طبية وتجميلية')], max_length=20, verbose_name='استخدام العدسة')),
                ('color', models.CharField(max_length=50, verbose_name='اللون')),
                ('power', models.CharField(blank=True, max_length=20, verbose_name='القوة')),
                ('diameter', models.CharField(blank=True, max_length=20, verbose_name='القطر')),
                ('base_curve', models.CharField(blank=True, max_length=20, verbose_name='انحناء القاعدة')),
                ('stock_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المتوفرة')),
                ('min_stock_level', models.PositiveIntegerField(default=10, verbose_name='الحد الأدنى للمخزون')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مميز')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.brand', verbose_name='العلامة التجارية')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='products.category', verbose_name='الفئة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/', verbose_name='الصورة')),
                ('alt_text', models.CharField(blank=True, max_length=200, verbose_name='النص البديل')),
                ('is_primary', models.BooleanField(default=False, verbose_name='صورة رئيسية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة منتج',
                'verbose_name_plural': 'صور المنتجات',
                'ordering': ['-is_primary', 'created_at'],
            },
        ),
    ]
