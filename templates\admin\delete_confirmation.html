{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<style>
    .delete-confirmation-container {
        max-width: 600px;
        margin: 2rem auto;
    }
    
    .delete-warning {
        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
        border: 2px solid var(--danger-color);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.2);
    }
    
    .delete-icon {
        width: 80px;
        height: 80px;
        background: var(--danger-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem auto;
        font-size: 2rem;
        color: white;
    }
    
    .delete-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--danger-color);
        margin-bottom: 1rem;
    }
    
    .delete-message {
        font-size: 1.1rem;
        color: #666;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }
    
    .delete-object-name {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #ddd;
        font-weight: 600;
        color: var(--primary-color);
        margin: 1rem 0;
    }
    
    .related-objects {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .related-objects h3 {
        color: var(--warning-color);
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .related-objects ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .related-objects li {
        background: #f8f9fa;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        margin-bottom: 0.5rem;
        border-left: 4px solid var(--warning-color);
    }
    
    .delete-actions {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        text-align: center;
    }
    
    .delete-actions h3 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
        font-weight: 600;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        color: white;
    }
    
    .btn-cancel {
        background: linear-gradient(135deg, var(--secondary-color), #5dade2);
        color: white;
        border: none;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }
    
    .btn-cancel:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        color: white;
    }
    
    .safety-note {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1.5rem;
        color: #856404;
        font-size: 0.9rem;
        text-align: right;
    }
    
    .safety-note i {
        color: var(--warning-color);
        margin-left: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .delete-confirmation-container {
            margin: 1rem;
        }
        
        .delete-warning,
        .related-objects,
        .delete-actions {
            padding: 1.5rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .btn-delete,
        .btn-cancel {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-confirmation-container">
    <div class="delete-warning">
        <div class="delete-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h1 class="delete-title">تأكيد الحذف</h1>
        <p class="delete-message">
            هل أنت متأكد من حذف العنصر التالي؟ هذا الإجراء لا يمكن التراجع عنه.
        </p>
        <div class="delete-object-name">
            {{ object_name }}: "{{ object }}"
        </div>
    </div>
    
    {% if deleted_objects %}
    <div class="related-objects">
        <h3>
            <i class="fas fa-link"></i>
            العناصر المرتبطة التي سيتم حذفها أيضاً
        </h3>
        <ul>
            {% for deleted_object in deleted_objects %}
                <li>{{ deleted_object }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}
    
    <div class="delete-actions">
        <h3>اختر الإجراء المناسب</h3>
        
        <form method="post">
            {% csrf_token %}
            <div class="action-buttons">
                <input type="submit" value="نعم، احذف نهائياً" class="btn-delete">
                <a href="../" class="btn-cancel">
                    <i class="fas fa-arrow-right me-2"></i>إلغاء والعودة
                </a>
            </div>
            
            <div class="safety-note">
                <i class="fas fa-shield-alt"></i>
                <strong>ملاحظة أمان:</strong> 
                تأكد من أنك تريد حذف هذا العنصر فعلاً. بعد الحذف، لن تتمكن من استرداد البيانات.
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأكيد إضافي عند النقر على زر الحذف
    const deleteBtn = document.querySelector('.btn-delete');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function(e) {
            const confirmed = confirm('هل أنت متأكد تماماً من حذف هذا العنصر؟ هذا الإجراء نهائي ولا يمكن التراجع عنه.');
            if (!confirmed) {
                e.preventDefault();
            }
        });
    }
    
    // إضافة اختصار لوحة المفاتيح للإلغاء (Escape)
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            window.location.href = '../';
        }
    });
});
</script>
{% endblock %}
