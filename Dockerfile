# استخدام Python 3.11 كصورة أساسية
FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PORT=8080

# إنشاء مجلد العمل
WORKDIR /app

# نسخ ملفات المتطلبات
COPY requirements_cloud.txt .

# تثبيت المتطلبات
RUN pip install --no-cache-dir -r requirements_cloud.txt

# نسخ ملفات المشروع
COPY . .

# جمع الملفات الثابتة
RUN python manage.py collectstatic --noinput

# إنشاء مستخدم غير root
RUN adduser --disabled-password --gecos '' appuser && chown -R appuser /app
USER appuser

# تشغيل الخادم
CMD exec gunicorn --bind :$PORT --workers 1 --threads 8 --timeout 0 visionlens_store.wsgi:application
