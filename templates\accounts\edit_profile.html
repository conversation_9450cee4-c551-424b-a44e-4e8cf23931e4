{% extends 'base/base.html' %}
{% load static %}

{% block title %}تعديل الملف الشخصي - عدستي{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
            <li class="breadcrumb-item active">تعديل الملف الشخصي</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">تعديل الملف الشخصي</h4>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        {% if form.errors %}
                            <div class="alert alert-danger">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Avatar -->
                        <div class="text-center mb-4">
                            {% if user.userprofile.avatar %}
                                <img src="{{ user.userprofile.avatar.url }}" class="rounded-circle mb-3" width="120" height="120" alt="الصورة الشخصية">
                            {% else %}
                                <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 120px; height: 120px;">
                                    <i class="fas fa-user fa-3x text-white"></i>
                                </div>
                            {% endif %}
                            <div>
                                <label for="{{ form.avatar.id_for_label }}" class="form-label">الصورة الشخصية</label>
                                <input type="file" class="form-control" id="{{ form.avatar.id_for_label }}" name="{{ form.avatar.name }}" accept="image/*">
                                <small class="form-text text-muted">اختر صورة بحجم أقل من 2 ميجابايت</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="{{ form.phone.id_for_label }}" name="{{ form.phone.name }}" value="{{ form.phone.value|default:'' }}" placeholder="+964 ************">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.birth_date.id_for_label }}" class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" id="{{ form.birth_date.id_for_label }}" name="{{ form.birth_date.name }}" value="{{ form.birth_date.value|default:'' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.gender.id_for_label }}" class="form-label">الجنس</label>
                                    <select class="form-select" id="{{ form.gender.id_for_label }}" name="{{ form.gender.name }}">
                                        <option value="">اختر الجنس</option>
                                        <option value="male" {% if form.gender.value == 'male' %}selected{% endif %}>ذكر</option>
                                        <option value="female" {% if form.gender.value == 'female' %}selected{% endif %}>أنثى</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.preferred_language.id_for_label }}" class="form-label">اللغة المفضلة</label>
                                    <select class="form-select" id="{{ form.preferred_language.id_for_label }}" name="{{ form.preferred_language.name }}">
                                        <option value="ar" {% if form.preferred_language.value == 'ar' %}selected{% endif %}>العربية</option>
                                        <option value="en" {% if form.preferred_language.value == 'en' %}selected{% endif %}>English</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="{{ form.newsletter_subscription.id_for_label }}" name="{{ form.newsletter_subscription.name }}" {% if form.newsletter_subscription.value %}checked{% endif %}>
                                <label class="form-check-label" for="{{ form.newsletter_subscription.id_for_label }}">
                                    أرغب في تلقي النشرة الإخبارية والعروض الخاصة
                                </label>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
