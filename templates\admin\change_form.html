{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}
{{ block.super }}
<style>
    .change-form-header {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        display: flex;
        justify-content: between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .change-form-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--primary-color);
        margin: 0;
        flex-grow: 1;
    }
    
    .change-form-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .form-container {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }
    
    .form-header {
        background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
        color: white;
        padding: 1.25rem 1.5rem;
    }
    
    .form-header h2 {
        margin: 0;
        font-weight: 600;
    }
    
    .form-content {
        padding: 2rem;
    }
    
    .form-row {
        margin-bottom: 2rem;
        padding: 1.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .form-row label {
        display: block;
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.75rem;
        font-size: 1rem;
    }
    
    .form-row .help {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.5rem;
        font-style: italic;
    }
    
    .form-row input[type="text"],
    .form-row input[type="password"],
    .form-row input[type="email"],
    .form-row input[type="url"],
    .form-row input[type="number"],
    .form-row input[type="tel"],
    .form-row input[type="date"],
    .form-row input[type="datetime-local"],
    .form-row textarea,
    .form-row select {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
        font-family: 'Cairo', sans-serif;
    }
    
    .form-row input:focus,
    .form-row textarea:focus,
    .form-row select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        outline: none;
    }
    
    .form-row textarea {
        min-height: 120px;
        resize: vertical;
    }
    
    .form-row .checkbox-row {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-top: 0.5rem;
    }
    
    .form-row input[type="checkbox"] {
        width: auto;
        transform: scale(1.3);
        margin: 0;
    }
    
    .form-row .checkbox-row label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }
    
    .errorlist {
        background: linear-gradient(135deg, #ffe6e6, #ffcccc);
        color: var(--danger-color);
        padding: 1rem;
        border-radius: 8px;
        margin-top: 0.75rem;
        border-left: 4px solid var(--danger-color);
    }
    
    .errorlist ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }
    
    .errorlist li {
        margin: 0;
        font-weight: 500;
    }
    
    .submit-row {
        background: #f8f9fa;
        padding: 1.5rem 2rem;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }
    
    .submit-row input[type="submit"],
    .submit-row .button {
        padding: 1rem 2rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        font-size: 1rem;
    }
    
    .submit-row .default {
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
        color: white;
    }
    
    .submit-row .default:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
    }
    
    .submit-row .deletelink {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
        color: white;
    }
    
    .submit-row .deletelink:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
    }
    
    .submit-row .button {
        background: linear-gradient(135deg, var(--secondary-color), #5dade2);
        color: white;
    }
    
    .submit-row .button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(52, 152, 219, 0.3);
    }
    
    .inline-group {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .inline-group h2 {
        color: var(--primary-color);
        font-weight: 600;
        margin-bottom: 1rem;
    }
    
    .inline-related {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
    }
    
    .file-upload {
        border: 2px dashed #e9ecef;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload:hover {
        border-color: var(--secondary-color);
        background: rgba(52, 152, 219, 0.05);
    }
    
    .file-upload input[type="file"] {
        display: none;
    }
    
    .file-upload-icon {
        font-size: 2rem;
        color: var(--secondary-color);
        margin-bottom: 1rem;
    }
    
    .file-upload-text {
        color: #6c757d;
        font-weight: 500;
    }
    
    @media (max-width: 768px) {
        .change-form-header {
            flex-direction: column;
            align-items: stretch;
        }
        
        .change-form-actions {
            justify-content: center;
        }
        
        .form-content {
            padding: 1.5rem;
        }
        
        .submit-row {
            padding: 1rem;
            flex-direction: column;
        }
        
        .submit-row input[type="submit"],
        .submit-row .button {
            width: 100%;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content_title %}{% endblock %}

{% block content %}
<div class="change-form-header">
    <h1 class="change-form-title">
        <i class="fas fa-{% if opts.model_name == 'product' %}box{% elif opts.model_name == 'order' %}shopping-cart{% elif opts.model_name == 'user' %}user{% elif opts.model_name == 'review' %}star{% else %}edit{% endif %} me-2"></i>
        {% if add %}
            إضافة {{ opts.verbose_name }}
        {% else %}
            تعديل {{ opts.verbose_name }}: {{ original|truncatechars:30 }}
        {% endif %}
    </h1>
    <div class="change-form-actions">
        {% if not add and has_view_permission %}
            <a href="{% url opts|admin_urlname:'changelist' %}" class="button">
                <i class="fas fa-list me-2"></i>عرض الكل
            </a>
        {% endif %}
        {% if not add and has_add_permission %}
            <a href="{% url opts|admin_urlname:'add' %}" class="button">
                <i class="fas fa-plus me-2"></i>إضافة جديد
            </a>
        {% endif %}
    </div>
</div>

{% if errors %}
    <div class="form-container">
        <div class="form-header" style="background: linear-gradient(135deg, var(--danger-color), #c0392b);">
            <h2><i class="fas fa-exclamation-triangle me-2"></i>يرجى تصحيح الأخطاء التالية</h2>
        </div>
        <div class="form-content">
            {{ errors }}
        </div>
    </div>
{% endif %}

<form method="post"{% if has_file_field %} enctype="multipart/form-data"{% endif %} novalidate>
    {% csrf_token %}
    
    <div class="form-container">
        <div class="form-header">
            <h2>
                <i class="fas fa-edit me-2"></i>
                {% if add %}معلومات {{ opts.verbose_name }} الجديد{% else %}تعديل معلومات {{ opts.verbose_name }}{% endif %}
            </h2>
        </div>
        <div class="form-content">
            {% if adminform.form.non_field_errors %}
                <div class="errorlist">
                    {{ adminform.form.non_field_errors }}
                </div>
            {% endif %}
            
            {% for fieldset in adminform %}
                {% if fieldset.name %}
                    <h3 style="color: var(--primary-color); margin: 2rem 0 1rem 0; font-weight: 600;">
                        {{ fieldset.name }}
                    </h3>
                {% endif %}
                
                {% for line in fieldset %}
                    <div class="form-row{% if line.fields|length_is:'1' and line.errors %} errors{% endif %}">
                        {% for field in line %}
                            {% if field.field.name %}
                                <div class="field-wrapper">
                                    {{ field.label_tag }}
                                    
                                    {% if field.is_checkbox %}
                                        <div class="checkbox-row">
                                            {{ field.field }}
                                            <label for="{{ field.field.id_for_label }}">{{ field.field.help_text }}</label>
                                        </div>
                                    {% elif field.field.widget.input_type == 'file' %}
                                        <div class="file-upload" onclick="document.getElementById('{{ field.field.id_for_label }}').click();">
                                            <div class="file-upload-icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </div>
                                            <div class="file-upload-text">
                                                انقر لاختيار ملف أو اسحب الملف هنا
                                            </div>
                                            {{ field.field }}
                                        </div>
                                    {% else %}
                                        {{ field.field }}
                                    {% endif %}
                                    
                                    {% if field.field.help_text and not field.is_checkbox %}
                                        <div class="help">{{ field.field.help_text }}</div>
                                    {% endif %}
                                    
                                    {% if field.field.errors %}
                                        <div class="errorlist">
                                            <ul>
                                                {% for error in field.field.errors %}
                                                    <li>{{ error }}</li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
        
        <div class="submit-row">
            {% if show_delete_link %}
                <a href="{% url opts|admin_urlname:'delete' original.pk|admin_urlquote %}" class="deletelink">
                    <i class="fas fa-trash me-2"></i>حذف
                </a>
            {% endif %}
            
            {% if show_save_as_new %}
                <input type="submit" value="حفظ كجديد" class="button" name="_saveasnew">
            {% endif %}
            
            {% if show_save_and_add_another %}
                <input type="submit" value="حفظ وإضافة آخر" class="button" name="_addanother">
            {% endif %}
            
            {% if show_save_and_continue %}
                <input type="submit" value="حفظ ومتابعة التعديل" class="button" name="_continue">
            {% endif %}
            
            <input type="submit" value="{% if add %}إضافة{% else %}حفظ{% endif %}" class="default" name="_save">
        </div>
    </div>
</form>

{% if adminform.form.media %}
    {{ adminform.form.media }}
{% endif %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // File upload preview
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const uploadDiv = e.target.closest('.file-upload');
                const textDiv = uploadDiv.querySelector('.file-upload-text');
                textDiv.textContent = `تم اختيار: ${file.name}`;
            }
        });
    });
    
    // Auto-save draft (optional)
    const form = document.querySelector('form');
    if (form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('change', function() {
                // يمكن إضافة وظيفة الحفظ التلقائي هنا
            });
        });
    }
});
</script>
{% endblock %}
