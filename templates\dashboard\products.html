{% extends 'dashboard/base.html' %}

{% block title %}إدارة المنتجات - عدستي{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">إدارة المنتجات</h1>
            <p class="page-subtitle">إدارة وتحرير منتجات المتجر</p>
        </div>
        <div>
            <a href="{% url 'admin:products_product_add' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="stat-card">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" class="form-control" name="search" value="{{ search_query }}" placeholder="اسم المنتج أو الوصف">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الفئة</label>
                    <select class="form-select" name="category">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">العلامة التجارية</label>
                    <select class="form-select" name="brand">
                        <option value="">جميع العلامات</option>
                        {% for brand in brands %}
                        <option value="{{ brand.id }}" {% if brand_filter == brand.id|stringformat:"s" %}selected{% endif %}>
                            {{ brand.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                        <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                        <option value="low_stock" {% if status_filter == 'low_stock' %}selected{% endif %}>مخزون منخفض</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <a href="{% url 'dashboard:products' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>مسح
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Products Table -->
<div class="row">
    <div class="col-12">
        <div class="table-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    المنتجات ({{ products.paginator.count }} منتج)
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>العلامة التجارية</th>
                                <th>السعر</th>
                                <th>المخزون</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    {% if product.image %}
                                        <img src="{{ product.image.url }}" alt="{{ product.name }}" 
                                             class="rounded" style="width: 50px; height: 50px; object-fit: cover;">
                                    {% else %}
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                             style="width: 50px; height: 50px;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ product.name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                                </td>
                                <td>{{ product.category.name }}</td>
                                <td>{{ product.brand.name }}</td>
                                <td>{{ product.price|floatformat:0 }} د.ع</td>
                                <td>
                                    {% if product.stock_quantity <= 10 %}
                                        <span class="badge bg-danger">{{ product.stock_quantity }}</span>
                                    {% elif product.stock_quantity <= 50 %}
                                        <span class="badge bg-warning">{{ product.stock_quantity }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ product.stock_quantity }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>{{ product.created_at|date:"Y/m/d" }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'products:detail' product.id %}" 
                                           class="btn btn-sm btn-outline-primary" target="_blank" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'admin:products_product_change' product.id %}" 
                                           class="btn btn-sm btn-primary" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                onclick="confirmDelete('{{ product.name }}', '{% url 'admin:products_product_delete' product.id %}')" 
                                                title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center text-muted py-5">
                                    <i class="fas fa-box fa-3x mb-3"></i>
                                    <br>
                                    لا توجد منتجات تطابق معايير البحث
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Pagination -->
            {% if products.has_other_pages %}
            <div class="card-footer">
                <nav aria-label="تصفح المنتجات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if products.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ products.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ products.number }} من {{ products.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ products.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ products.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if brand_filter %}&brand={{ brand_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المنتج "<span id="productName"></span>"؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">حذف</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(productName, deleteUrl) {
    document.getElementById('productName').textContent = productName;
    document.getElementById('confirmDeleteBtn').href = deleteUrl;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
