{% extends 'base/base.html' %}
{% load static %}
{% load cart_extras %}

{% block title %}سلة التسوق - عدستي{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
            <li class="breadcrumb-item active">سلة التسوق</li>
        </ol>
    </nav>

    <h2 class="mb-4">سلة التسوق</h2>

    {% if cart_items %}
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">المنتجات في السلة ({{ cart_items|length }})</h5>
                </div>
                <div class="card-body p-0">
                    {% for item in cart_items %}
                    <div class="cart-item p-3 {% if not forloop.last %}border-bottom{% endif %}">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                {% if item.product.images.first %}
                                    <img src="{{ item.product.images.first.image.url }}" class="img-fluid rounded" alt="{{ item.product.name }}" style="height: 80px; object-fit: cover;">
                                {% else %}
                                    <img src="{% static 'images/no-image.svg' %}" class="img-fluid rounded" alt="{{ item.product.name }}" style="height: 80px; object-fit: cover;">
                                {% endif %}
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-1">
                                    <a href="{% url 'products:detail' item.product.pk %}" class="text-decoration-none">{{ item.product.name }}</a>
                                </h6>
                                <small class="text-muted">{{ item.product.brand.name }}</small>
                                <br>
                                <small class="text-muted">{{ item.product.get_lens_type_display }} - {{ item.product.color }}</small>
                            </div>
                            <div class="col-md-2">
                                <span class="fw-bold">{{ item.product.get_price }} د.ع</span>
                            </div>
                            <div class="col-md-2">
                                <div class="input-group" style="width: 120px;">
                                    <button class="btn btn-outline-secondary btn-sm quantity-minus" type="button">-</button>
                                    <input type="number" class="form-control form-control-sm text-center quantity-input" value="{{ item.quantity }}" min="1" max="{{ item.product.stock_quantity }}" data-cart-item-id="{{ item.id }}" data-original-value="{{ item.quantity }}">
                                    <button class="btn btn-outline-secondary btn-sm quantity-plus" type="button">+</button>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <span class="fw-bold item-total">{{ item.total_price }} د.ع</span>
                            </div>
                            <div class="col-md-1">
                                <form method="post" action="{% url 'orders:remove_from_cart' item.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger btn-sm" onclick="return confirm('هل أنت متأكد من إزالة هذا المنتج؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Continue Shopping -->
            <div class="mt-3">
                <a href="{% url 'products:list' %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>متابعة التسوق
                </a>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    {% if cart %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span class="cart-total">{{ cart.total_price }} د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span>
                            {% if cart.total_price >= 50000 %}
                                <span class="text-success">مجاني</span>
                            {% else %}
                                5,000 د.ع
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span>0 د.ع</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>الإجمالي:</strong>
                        <strong>
                            {% if cart.total_price >= 50000 %}
                                {{ cart.total_price }} د.ع
                            {% else %}
                                {{ cart.total_price|add:5000 }} د.ع
                            {% endif %}
                        </strong>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>المجموع الفرعي:</span>
                        <span>0 د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الشحن:</span>
                        <span>5,000 د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الضريبة:</span>
                        <span>0 د.ع</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between mb-3">
                        <strong>الإجمالي:</strong>
                        <strong>5,000 د.ع</strong>
                    </div>
                    {% endif %}

                    {% if cart and cart.total_price < 50000 %}
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            أضف {{ cart.total_price|free_shipping_remaining }} د.ع أخرى للحصول على شحن مجاني!
                        </small>
                    </div>
                    {% endif %}

                    {% if cart and cart_items %}
                    <a href="{% url 'orders:checkout' %}" class="btn btn-primary w-100 btn-lg">
                        <i class="fas fa-credit-card me-2"></i>إتمام الطلب
                    </a>
                    {% else %}
                    <button class="btn btn-secondary w-100 btn-lg" disabled>
                        <i class="fas fa-credit-card me-2"></i>السلة فارغة
                    </button>
                    {% endif %}

                    <!-- Payment Methods -->
                    <div class="mt-3 text-center">
                        <small class="text-muted">طرق الدفع المقبولة:</small>
                        <div class="mt-2">
                            <i class="fab fa-cc-visa fa-2x text-primary me-2"></i>
                            <i class="fab fa-cc-mastercard fa-2x text-warning me-2"></i>
                            <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Badge -->
            <div class="card mt-3">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                    <h6>تسوق آمن</h6>
                    <small class="text-muted">معلوماتك محمية بأحدث تقنيات الأمان</small>
                </div>
            </div>
        </div>
    </div>

    {% else %}
    <!-- Empty Cart -->
    <div class="text-center py-5">
        <i class="fas fa-shopping-cart fa-5x text-muted mb-4"></i>
        <h3>سلة التسوق فارغة</h3>
        <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
        <a href="{% url 'products:list' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-shopping-bag me-2"></i>ابدأ التسوق الآن
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Update cart item quantity
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('.quantity-input');
    
    quantityInputs.forEach(input => {
        const minusBtn = input.parentElement.querySelector('.quantity-minus');
        const plusBtn = input.parentElement.querySelector('.quantity-plus');
        
        minusBtn.addEventListener('click', function() {
            let currentValue = parseInt(input.value) || 1;
            if (currentValue > 1) {
                input.value = currentValue - 1;
                updateCartItem(input);
            }
        });
        
        plusBtn.addEventListener('click', function() {
            let currentValue = parseInt(input.value) || 1;
            let maxValue = parseInt(input.getAttribute('max')) || 999;
            if (currentValue < maxValue) {
                input.value = currentValue + 1;
                updateCartItem(input);
            }
        });
        
        input.addEventListener('focus', function() {
            this.setAttribute('data-original-value', this.value);
        });

        input.addEventListener('change', function() {
            updateCartItem(this);
        });
    });
});

function updateCartItem(input) {
    const cartItemId = input.dataset.cartItemId;
    const quantity = parseInt(input.value);

    if (!cartItemId) {
        console.error('Cart item ID not found');
        return;
    }

    fetch('/orders/cart/update/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            cart_item_id: cartItemId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.item_removed) {
                // إزالة العنصر من الصفحة
                input.closest('.cart-item').remove();

                // التحقق من وجود عناصر أخرى
                const remainingItems = document.querySelectorAll('.cart-item');
                if (remainingItems.length === 0) {
                    location.reload(); // إعادة تحميل الصفحة لعرض السلة الفارغة
                }
            } else {
                // تحديث إجمالي العنصر
                const itemTotal = input.closest('.cart-item').querySelector('.item-total');
                if (itemTotal) {
                    itemTotal.textContent = data.item_total;
                }
            }

            // تحديث إجمالي السلة
            const cartTotal = document.querySelector('.cart-total');
            if (cartTotal) {
                cartTotal.textContent = data.cart_total;
            }

            // تحديث عداد السلة في الشريط العلوي
            updateCartCount(data.cart_count);
        } else {
            alert(data.message || 'حدث خطأ أثناء تحديث السلة');
            // إعادة القيمة السابقة
            input.value = input.getAttribute('data-original-value') || 1;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
        // إعادة القيمة السابقة
        input.value = input.getAttribute('data-original-value') || 1;
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function updateCartCount(count) {
    const cartBadge = document.querySelector('.navbar .badge');
    if (cartBadge) {
        cartBadge.textContent = count;
    }
}
</script>
{% endblock %}
