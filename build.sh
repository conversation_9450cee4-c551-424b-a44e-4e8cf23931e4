#!/bin/bash

# Install dependencies
pip install -r requirements_final.txt

# Collect static files
python manage.py collectstatic --noinput

# Run migrations
python manage.py migrate --noinput

# Create superuser if it doesn't exist
echo "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', 'admin123')" | python manage.py shell

echo "Build completed successfully!"
