# Generated by Django 4.2.7 on 2025-06-06 15:57

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='عدستي', max_length=100, verbose_name='اسم الموقع')),
                ('site_description', models.TextField(default='متجر العدسات اللاصقة الأول في العراق', verbose_name='وصف الموقع')),
                ('contact_email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='البريد الإلكتروني')),
                ('contact_phone', models.CharField(default='+964 ************', max_length=20, verbose_name='رقم الهاتف')),
                ('address', models.TextField(default='بغداد، العراق', verbose_name='العنوان')),
                ('free_shipping_threshold', models.DecimalField(decimal_places=2, default=50000, max_digits=10, verbose_name='حد الشحن المجاني')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=5000, max_digits=10, verbose_name='تكلفة الشحن')),
                ('primary_color', models.CharField(default='#007bff', max_length=7, verbose_name='اللون الأساسي')),
                ('secondary_color', models.CharField(default='#6c757d', max_length=7, verbose_name='اللون الثانوي')),
                ('meta_keywords', models.TextField(blank=True, verbose_name='الكلمات المفتاحية')),
                ('meta_description', models.TextField(blank=True, verbose_name='وصف الميتا')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'إعدادات لوحة التحكم',
                'verbose_name_plural': 'إعدادات لوحة التحكم',
            },
        ),
        migrations.CreateModel(
            name='AdminActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج')], max_length=20, verbose_name='النشاط')),
                ('model_name', models.CharField(blank=True, max_length=100, verbose_name='اسم النموذج')),
                ('object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'نشاط إداري',
                'verbose_name_plural': 'الأنشطة الإدارية',
                'ordering': ['-timestamp'],
            },
        ),
    ]
