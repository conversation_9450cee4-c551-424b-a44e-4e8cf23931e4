# 👁️ عدستي - متجر العدسات اللاصقة الإلكتروني

متجر إلكتروني متطور لبيع العدسات اللاصقة مبني بـ Django مع لوحة تحكم احترافية ونظام إدارة متكامل.

## 🌟 المميزات الرئيسية

### 🛒 **المتجر الإلكتروني**
- **واجهة عصرية**: تصميم متجاوب وجذاب
- **دعم ثنائي اللغة**: العربية والإنجليزية
- **نظام بحث متقدم**: بحث ذكي في المنتجات
- **فلترة متطورة**: حسب الفئة، العلامة التجارية، السعر
- **سلة تسوق تفاعلية**: إضافة وإزالة المنتجات بسهولة
- **نظام دفع آمن**: دعم متعدد لطرق الدفع
- **تتبع الطلبات**: متابعة حالة الطلب في الوقت الفعلي

### 📊 **لوحة التحكم المتقدمة**
- **إحصائيات مباشرة**: مبيعات، طلبات، عملاء
- **رسوم بيانية تفاعلية**: تحليل البيانات بصرياً
- **إدارة المنتجات**: إضافة وتعديل المنتجات والصور
- **إدارة الطلبات**: متابعة ومعالجة الطلبات
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **تقارير مفصلة**: تقارير مبيعات وأداء

### 🔧 **نظام الإدارة**
- **Django Admin محسن**: واجهة إدارة مخصصة وجميلة
- **إدارة الصور**: نظام متطور لإدارة صور المنتجات
- **إدارة المخزون**: تتبع الكميات والتنبيهات
- **إدارة الفئات**: تنظيم المنتجات بشكل هرمي
- **إدارة العلامات التجارية**: تصنيف حسب الماركات

## 🚀 التقنيات المستخدمة

### Backend
- **Django 4.2**: إطار العمل الرئيسي
- **Python 3.8+**: لغة البرمجة
- **SQLite/PostgreSQL**: قاعدة البيانات
- **Django REST Framework**: APIs
- **Pillow**: معالجة الصور

### Frontend
- **HTML5 & CSS3**: البنية والتصميم
- **Bootstrap 5**: إطار العمل للتصميم
- **JavaScript**: التفاعل والديناميكية
- **Chart.js**: الرسوم البيانية
- **Font Awesome**: الأيقونات
- **Google Fonts (Cairo)**: الخطوط العربية

### أدوات إضافية
- **Git**: نظام التحكم في الإصدارات
- **GitHub**: استضافة الكود
- **VS Code**: بيئة التطوير

## 📦 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone https://github.com/Yaser25m/Visionlens.git
cd Visionlens
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate  # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء مستخدم مدير
```bash
python manage.py createsuperuser
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

### 7. الوصول للموقع
- **الموقع الرئيسي**: http://127.0.0.1:8000/
- **لوحة التحكم**: http://127.0.0.1:8000/dashboard/
- **الإدارة المتقدمة**: http://127.0.0.1:8000/admin/

## 🌐 النشر المباشر

### الموقع المنشور على Cloudflare Pages:
- **الرابط المباشر**: https://1477d897.visionlens1.pages.dev/
- **لوحة الإدارة**: https://1477d897.visionlens1.pages.dev/admin/
- **لوحة التحكم**: https://1477d897.visionlens1.pages.dev/dashboard/

### معلومات تسجيل الدخول:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
Visionlens/
├── visionlens_store/          # إعدادات المشروع الرئيسية
├── products/                  # تطبيق المنتجات
├── orders/                    # تطبيق الطلبات
├── accounts/                  # تطبيق المستخدمين
├── dashboard/                 # تطبيق لوحة التحكم
├── templates/                 # قوالب HTML
├── static/                    # الملفات الثابتة
├── media/                     # ملفات الوسائط
├── requirements.txt           # متطلبات المشروع
└── manage.py                  # ملف إدارة Django
```

## 🎯 الميزات المتقدمة

### 🔐 الأمان
- **حماية CSRF**: حماية من هجمات Cross-Site Request Forgery
- **تشفير كلمات المرور**: باستخدام Django's built-in hashing
- **جلسات آمنة**: إدارة آمنة لجلسات المستخدمين
- **تحقق من الصلاحيات**: نظام صلاحيات متدرج

### 📱 التجاوب
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **تحسين للموبايل**: تجربة مستخدم محسنة للهواتف
- **سرعة التحميل**: تحسين الأداء والسرعة

### 🌐 SEO
- **URLs صديقة لمحركات البحث**: روابط واضحة ومفهومة
- **Meta tags**: عناوين ووصف محسن
- **Sitemap**: خريطة موقع للفهرسة
- **Schema markup**: بيانات منظمة للمنتجات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👨‍💻 المطور

**ياسر محمد**
- GitHub: [@Yaser25m](https://github.com/Yaser25m)
- Email: <EMAIL>

## 🙏 شكر وتقدير

- **Django Community**: للإطار الرائع
- **Bootstrap Team**: للتصميم المتجاوب
- **Font Awesome**: للأيقونات الجميلة
- **Chart.js**: للرسوم البيانية التفاعلية

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

---

**🎉 شكراً لاستخدام عدستي! نتمنى لك تجربة رائعة.**
