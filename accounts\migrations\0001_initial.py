# Generated by Django 4.2.7 on 2025-06-06 14:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('gender', models.CharField(blank=True, choices=[('male', 'ذكر'), ('female', 'أنثى')], max_length=10, verbose_name='الجنس')),
                ('avatar', models.ImageField(blank=True, upload_to='avatars/', verbose_name='الصورة الشخصية')),
                ('preferred_language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة المفضلة')),
                ('newsletter_subscription', models.BooleanField(default=True, verbose_name='اشتراك النشرة الإخبارية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف شخصي',
                'verbose_name_plural': 'الملفات الشخصية',
            },
        ),
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50, verbose_name='عنوان العنوان')),
                ('full_name', models.CharField(max_length=100, verbose_name='الاسم الكامل')),
                ('phone', models.CharField(max_length=20, verbose_name='رقم الهاتف')),
                ('address_line_1', models.CharField(max_length=200, verbose_name='العنوان الأول')),
                ('address_line_2', models.CharField(blank=True, max_length=200, verbose_name='العنوان الثاني')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('state', models.CharField(max_length=50, verbose_name='المنطقة/الولاية')),
                ('postal_code', models.CharField(blank=True, max_length=10, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='السعودية', max_length=50, verbose_name='البلد')),
                ('is_default', models.BooleanField(default=False, verbose_name='عنوان افتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'عنوان',
                'verbose_name_plural': 'العناوين',
                'ordering': ['-is_default', '-created_at'],
            },
        ),
    ]
