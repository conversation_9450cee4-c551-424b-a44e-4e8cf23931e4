# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
docs/

# Development files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
